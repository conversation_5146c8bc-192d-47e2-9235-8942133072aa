# Hello World Dify Plugin

这是一个简单的Hello World Dify插件，用于演示基本的插件开发流程。

## 功能

- **Hello工具**: 向用户发送问候消息，支持自定义姓名参数

## 使用方法

### 参数

- `name` (可选): 要问候的姓名，默认为"World"

### 示例

```
输入: name = "Alice"
输出: "Hello, Alice!"

输入: (无参数)
输出: "Hello, World!"
```

## 开发

### 本地测试

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行插件：
```bash
python main.py
```

### 打包

使用Dify CLI打包插件：
```bash
dify plugin package ./hello-world
```

## 文件结构

```
hello-world/
├── _assets/             # 图标和视觉资源
├── provider/            # 提供者定义和验证
│   ├── hello-world.py   # 凭证验证逻辑
│   └── hello-world.yaml # 提供者配置
├── tools/               # 工具实现
│   ├── hello.py         # Hello工具实现
│   └── hello.yaml       # Hello工具配置
├── main.py              # 入口文件
├── manifest.yaml        # 插件主配置
├── README.md            # 文档
└── requirements.txt     # 依赖列表
```
