identity:
  name: hello
  author: developer
  label:
    en_US: Hello World
    zh_Hans: Hello World
description:
  human:
    en_US: A simple tool that greets the user with a customizable message
    zh_Hans: 一个简单的工具，用可定制的消息向用户问候
  llm: A simple greeting tool that says hello to users with an optional custom name parameter.
parameters:
  - name: name
    type: string
    required: false
    label:
      en_US: Name
      zh_Hans: 姓名
    human_description:
      en_US: The name to greet (optional, defaults to "World")
      zh_Hans: 要问候的姓名（可选，默认为"World"）
    llm_description: The name of the person to greet. If not provided, defaults to "World".
    form: llm
extra:
  python:
    source: tools/hello.py
output_schema:
  type: object
  properties:
    greeting:
      type: string
      description: The greeting message
