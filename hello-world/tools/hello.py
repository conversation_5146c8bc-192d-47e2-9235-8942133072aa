from collections.abc import Generator
from typing import Any

from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage


class HelloTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        """
        Hello World工具的实现
        
        Args:
            tool_parameters: 工具参数字典
            
        Yields:
            ToolInvokeMessage: 工具调用消息
        """
        try:
            # 获取可选的name参数，如果没有提供则默认为"World"
            name = tool_parameters.get("name", "World")
            
            # 创建问候消息
            greeting = f"Hello, {name}!"
            
            # 返回文本消息
            yield self.create_text_message(greeting)
            
            # 返回JSON格式的结果
            yield self.create_json_message({
                "greeting": greeting,
                "name": name,
                "timestamp": "2025-06-06T12:10:00Z"
            })
            
            # 为工作流提供变量输出
            yield self.create_variable_message("greeting_result", greeting)
            
        except Exception as e:
            # 错误处理
            yield self.create_text_message(f"Error: {str(e)}")
